[package]
name = "tts_commands"
version = "0.1.0"
edition = "2021"
rust-version = "1.83"

[dependencies]
indexmap = "2"
strsim = "0.11"
num-format = "0.4"
futures-channel = "0.3.31"
uuid = { version = "1.17.0", features = ["v7"] }

sqlx.workspace = true
tokio.workspace = true
poise.workspace = true
anyhow.workspace = true
sysinfo.workspace = true
tracing.workspace = true
aformat.workspace = true
serenity.workspace = true
typesize.workspace = true
songbird.workspace = true
arrayvec.workspace = true

tts_core = { path = "../tts_core" }

[lints]
workspace = true
