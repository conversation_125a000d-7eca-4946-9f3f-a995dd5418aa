services:
    bot:
        image: Discord-TTS/Bot
        volumes:
          - type: bind
            source: ./config.toml
            target: /config.toml

        depends_on: [database, tts-service]
        network_mode: "host"
    database:
        image: postgres:13
        ports: [5432:5432]
        environment: 
            POSTGRES_USER: tts
            POSTGRES_PASSWORD: tts_password
    tts-service:
        image: gnomeddev/tts-service
        volumes:
          - type: bind
            source: ${GOOGLE_APPLICATION_CREDENTIALS}
            target: /gcp.json
        environment:
          -  IPV6_BLOCK
          -  LOG_LEVEL=INFO
          -  BIND_ADDR=0.0.0.0:20310
          -  GOOGLE_APPLICATION_CREDENTIALS=/gcp.json
        network_mode: "host"
        expose: [20310]
