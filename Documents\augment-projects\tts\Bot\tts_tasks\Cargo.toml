[package]
name = "tts_tasks"
version = "0.1.0"
edition = "2021"
rust-version = "1.83"

[dependencies]
serde_json = "1.0.111"

sqlx.workspace = true
tokio.workspace = true
serde.workspace = true
anyhow.workspace = true
tracing.workspace = true
reqwest.workspace = true
aformat.workspace = true
serenity.workspace = true
itertools.workspace = true
parking_lot.workspace = true
tracing-subscriber.workspace = true
console-subscriber.workspace = true

tts_core = { path = "../tts_core" }

[lints]
workspace = true
