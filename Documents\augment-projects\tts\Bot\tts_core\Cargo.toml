[package]
name = "tts_core"
version = "0.1.0"
edition = "2021"
rust-version = "1.83"

[dependencies]
rand = "0.9"
sha2 = "0.10"
linkify = "0.10"
bitflags = "2.4.1"
strum_macros = "0.27"
chrono = { version = "0.4.38", default-features = false }
bool_to_bitflags = { version = "0.1", features = ["typesize"] }

sqlx.workspace = true
regex.workspace = true
poise.workspace = true
serde.workspace = true
tokio.workspace = true
anyhow.workspace = true
aformat.workspace = true
sysinfo.workspace = true
tracing.workspace = true
dashmap.workspace = true
reqwest.workspace = true
arrayvec.workspace = true
typesize.workspace = true
songbird.workspace = true
serenity.workspace = true
mini-moka.workspace = true
itertools.workspace = true
parking_lot.workspace = true

[lints]
workspace = true

[package.metadata.cargo-machete]
ignored = [
    "bitflags", # Used in `bool_to_bitflags`
]
