[package]
name = "tts_bot"
version = "0.1.0"
edition = "2021"
rust-version = "1.83"

[workspace]
members = [
    "tts_core",
    "tts_commands",
    "tts_events",
    "tts_tasks",
    "tts_migrations",
]

[profile.release]
lto = "thin"
panic = "abort"
debug = 2

[profile.dev.package.syn]
opt-level = 3

[lints]
workspace = true

[dependencies]
const_format = "0.2"
jemallocator = "0.5.4"
small-fixed-array = { version = "0.4.5", features = [
    "nightly",
    "to-arraystring",
] }

serde.workspace = true
tokio.workspace = true
poise.workspace = true
anyhow.workspace = true
sysinfo.workspace = true
tracing.workspace = true
reqwest.workspace = true
dashmap.workspace = true
songbird.workspace = true
mini-moka.workspace = true
parking_lot.workspace = true
tracing-subscriber.workspace = true
console-subscriber.workspace = true

tts_core = { path = "tts_core" }
tts_tasks = { path = "tts_tasks" }
tts_events = { path = "tts_events" }
tts_commands = { path = "tts_commands" }
tts_migrations = { path = "tts_migrations" }

[dependencies.symphonia]
features = ["mp3", "ogg", "wav", "pcm"]
default-features = false
version = "0.5.3"

[workspace.dependencies]
regex = "1"
anyhow = "1"
serde = "1.0.209"
tracing = "0.1"
sysinfo = "0.33"
aformat = "0.1.3"
itertools = "0.14"
arrayvec = "0.7.6"
parking_lot = "0.12"
mini-moka = { version = "0.10.3", features = ["sync"] }
console-subscriber = { version = "0.4.1", features = ['parking_lot'] }
tracing-subscriber = { version = "0.3.19", features = ["parking_lot"] }
# TODO: Remove `dashmap` once mini_moka releases a breaking version with dashmap 6.
typesize = { version = "0.1.9", features = ["arrayvec", "dashmap", "details"] }

[workspace.dependencies.sqlx]
version = "0.8.1"
default-features = false
features = ["macros", "postgres", "runtime-tokio-rustls"]

[workspace.dependencies.reqwest]
version = "0.12.7"
default-features = false
features = ["rustls-tls"]

[workspace.dependencies.tokio]
version = "1.39.3"
features = ["rt-multi-thread", "signal", "parking_lot"]

[workspace.dependencies.dashmap]
version = "6.1.0"
default-features = false

[workspace.dependencies.serenity]
git = "https://github.com/serenity-rs/serenity"
default-features = false
branch = "next"
features = [
    "typesize",
    "temp_cache",
    "tokio_task_builder",
    "transport_compression_zstd",
]

[workspace.dependencies.poise]
git = "https://github.com/serenity-rs/poise"
features = ["cache"]
branch = "serenity-next"

[workspace.dependencies.songbird]
git = "https://github.com/serenity-rs/songbird"
features = ["builtin-queue"]
branch = "serenity-next"

[workspace.lints.rust]
rust_2018_idioms = "warn"
missing_copy_implementations = "warn"
noop_method_call = "warn"

[workspace.lints.clippy]
pedantic = { level = "warn", priority = -1 }
cast_sign_loss = "allow"
cast_possible_wrap = "allow"
cast_lossless = "allow"
cast_possible_truncation = "allow"
unreadable_literal = "allow"
wildcard_imports = "allow"
too_many_lines = "allow"
similar_names = "allow"
missing_panics_doc = "allow"
missing_errors_doc = "allow"
map_unwrap_or = "allow"

[package.metadata.cargo-machete]
ignored = [
    "const_format", # Used by database initialisation in macro
    "symphonia",    # Enabled for the features
]

[patch."https://github.com/serenity-rs/serenity".serenity]
git = "https://github.com/GnomedDev/serenity"
branch = "no-waiting-for-shutdown"
